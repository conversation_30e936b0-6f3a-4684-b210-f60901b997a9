using System.Globalization;
using CsvHelper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.MachineLearning.DataPrep;

/// <summary>
/// Service for exporting ML training features from database to CSV format.
/// Handles data preparation and feature engineering for ML model training.
/// </summary>
public interface IFeatureExportService
{
    /// <summary>
    /// Exports features to CSV file for ML training
    /// </summary>
    Task ExportCsvAsync(string path, DateTime from, DateTime to, float winThreshold = 0.01f);

    /// <summary>
    /// Exports features with additional metadata and statistics
    /// </summary>
    Task ExportEnhancedCsvAsync(string path, DateTime from, DateTime to,
        bool includeMetadata = true, float winThreshold = 0.01f);

    /// <summary>
    /// Exports position sizing features to CSV for ML training
    /// </summary>
    Task ExportPositionSizingCsvAsync(string path, DateTime from, DateTime to);

    /// <summary>
    /// Exports regime classification features to CSV for ML training
    /// </summary>
    Task ExportRegimeCsvAsync(string path, DateTime from, DateTime to);

    /// <summary>
    /// Exports slippage data to CSV for ML training
    /// </summary>
    Task ExportSlippageCsvAsync(string path, DateTime from, DateTime to);

    /// <summary>
    /// Gets feature export statistics
    /// </summary>
    Task<FeatureExportStats> GetExportStatsAsync(DateTime from, DateTime to, float winThreshold = 0.01f);

    /// <summary>
    /// Validates feature data quality before export
    /// </summary>
    Task<FeatureValidationResult> ValidateFeaturesAsync(DateTime from, DateTime to);
}

public sealed class FeatureExportService : IFeatureExportService
{
    private readonly IDbContextFactory<MLFeaturesDbContext> _dbContextFactory;
    private readonly ILogger<FeatureExportService> _logger;

    public FeatureExportService(
        IDbContextFactory<MLFeaturesDbContext> dbContextFactory,
        ILogger<FeatureExportService> logger)
    {
        _dbContextFactory = dbContextFactory;
        _logger = logger;
    }

    public async Task ExportCsvAsync(string path, DateTime from, DateTime to, float winThreshold = 0.01f)
    {
        _logger.LogInformation("Exporting features to CSV: {Path} from {From} to {To}", path, from, to);

        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var features = await context.Features
                .Where(f => f.Date >= from && f.Date <= to)
                .OrderBy(f => f.Date)
                .Select(f => new MLSignalInput
                {
                    Symbol = f.Symbol,
                    SmaGap = f.SmaGap,
                    Volatility = f.Volatility,
                    Rsi = f.Rsi,
                    BreadthScore = f.BreadthScore,
                    VixLevel = f.VixLevel,
                    SixMonthReturn = f.SixMonthReturn,
                    RelativeVolume = f.RelativeVolume,
                    MarketRegime = f.MarketRegime,
                    Win = f.Forward3DReturn >= winThreshold
                })
                .ToListAsync();

            if (!features.Any())
            {
                _logger.LogWarning("No features found for date range {From} to {To}", from, to);
                return;
            }

            // Ensure directory exists
            var directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Write CSV file
            using var writer = new StreamWriter(path);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            
            await csv.WriteRecordsAsync(features);

            _logger.LogInformation("Exported {Count} feature records to {Path}", features.Count, path);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export features to CSV: {Path}", path);
            throw;
        }
    }

    public async Task ExportEnhancedCsvAsync(string path, DateTime from, DateTime to, 
        bool includeMetadata = true, float winThreshold = 0.01f)
    {
        _logger.LogInformation("Exporting enhanced features to CSV: {Path}", path);

        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            
            var features = await context.Features
                .Where(f => f.Date >= from && f.Date <= to)
                .OrderBy(f => f.Date)
                .ToListAsync();

            if (!features.Any())
            {
                _logger.LogWarning("No features found for date range {From} to {To}", from, to);
                return;
            }

            // Ensure directory exists
            var directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Create enhanced records with additional features
            var enhancedFeatures = features.Select(f => new
            {
                f.Symbol,
                f.Date,
                f.SmaGap,
                f.Volatility,
                f.Rsi,
                f.BreadthScore,
                f.VixLevel,
                f.SixMonthReturn,
                f.RelativeVolume,
                f.MarketRegime,
                f.Forward3DReturn,
                f.Forward5DReturn,
                f.Forward10DReturn,
                f.MaxFavorableExcursion,
                f.MaxAdverseExcursion,
                Win = f.Forward3DReturn >= winThreshold,
                Win5D = f.Forward5DReturn >= winThreshold,
                Win10D = f.Forward10DReturn >= winThreshold,
                // Additional engineered features
                VolatilityRank = CalculateVolatilityRank(f.Volatility, features),
                RsiOverbought = f.Rsi > 70,
                RsiOversold = f.Rsi < 30,
                HighVix = f.VixLevel > 25,
                StrongMomentum = f.SixMonthReturn > 0.2f,
                f.CreatedAt
            }).ToList();

            // Write CSV file
            using var writer = new StreamWriter(path);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            
            await csv.WriteRecordsAsync(enhancedFeatures);

            // Write metadata file if requested
            if (includeMetadata)
            {
                var metadataPath = Path.ChangeExtension(path, ".metadata.json");
                var stats = await GetExportStatsAsync(from, to, winThreshold);
                var metadata = new
                {
                    ExportedAt = DateTime.UtcNow,
                    DateRange = new { From = from, To = to },
                    WinThreshold = winThreshold,
                    Stats = stats,
                    FeatureCount = enhancedFeatures.Count
                };

                await File.WriteAllTextAsync(metadataPath, 
                    System.Text.Json.JsonSerializer.Serialize(metadata, new System.Text.Json.JsonSerializerOptions 
                    { 
                        WriteIndented = true 
                    }));

                _logger.LogInformation("Exported metadata to {MetadataPath}", metadataPath);
            }

            _logger.LogInformation("Exported {Count} enhanced feature records to {Path}", 
                enhancedFeatures.Count, path);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export enhanced features to CSV: {Path}", path);
            throw;
        }
    }

    public async Task<FeatureExportStats> GetExportStatsAsync(DateTime from, DateTime to, float winThreshold = 0.01f)
    {
        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var features = await context.Features
            .Where(f => f.Date >= from && f.Date <= to)
            .ToListAsync();

        if (!features.Any())
        {
            return new FeatureExportStats(0, 0, 0, 0.0, new Dictionary<string, int>(), from, to);
        }

        var totalCount = features.Count;
        var winCount = features.Count(f => f.Forward3DReturn >= winThreshold);
        var lossCount = totalCount - winCount;
        var winRate = (double)winCount / totalCount;

        var symbolCounts = features
            .GroupBy(f => f.Symbol)
            .ToDictionary(g => g.Key, g => g.Count());

        return new FeatureExportStats(totalCount, winCount, lossCount, winRate, symbolCounts, from, to);
    }

    public async Task ExportPositionSizingCsvAsync(string path, DateTime from, DateTime to)
    {
        _logger.LogInformation("Exporting position sizing features to CSV: {Path}", path);

        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            var features = await context.Features
                .Where(f => f.Date >= from && f.Date <= to)
                .Where(f => f.RankProb > 0) // Only include records with valid ranking probability
                .OrderBy(f => f.Date)
                .Select(f => new PositionSizingRow(
                    f.RankProb,
                    f.ATR_Pct,
                    f.AvgSpreadPct,
                    f.EquityPctRisk
                ))
                .ToListAsync();

            if (!features.Any())
            {
                _logger.LogWarning("No position sizing features found for date range {From} to {To}", from, to);
                return;
            }

            // Ensure directory exists
            var directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Write to CSV
            using var writer = new StreamWriter(path);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            await csv.WriteRecordsAsync(features);

            _logger.LogInformation("Position sizing CSV export completed: {Count} records written to {Path}",
                features.Count, path);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export position sizing features to CSV: {Path}", path);
            throw;
        }
    }

    public async Task<FeatureValidationResult> ValidateFeaturesAsync(DateTime from, DateTime to)
    {
        using var context = await _dbContextFactory.CreateDbContextAsync();
        
        var features = await context.Features
            .Where(f => f.Date >= from && f.Date <= to)
            .ToListAsync();

        var issues = new List<string>();
        var warnings = new List<string>();

        if (!features.Any())
        {
            issues.Add("No features found in the specified date range");
            return new FeatureValidationResult(false, issues, warnings);
        }

        // Check for missing or invalid values
        var invalidSmaGap = features.Count(f => float.IsNaN(f.SmaGap) || float.IsInfinity(f.SmaGap));
        var invalidVolatility = features.Count(f => float.IsNaN(f.Volatility) || float.IsInfinity(f.Volatility) || f.Volatility < 0);
        var invalidRsi = features.Count(f => f.Rsi < 0 || f.Rsi > 100);
        var invalidVix = features.Count(f => f.VixLevel <= 0 || f.VixLevel > 100);

        if (invalidSmaGap > 0) issues.Add($"{invalidSmaGap} records have invalid SMA gap values");
        if (invalidVolatility > 0) issues.Add($"{invalidVolatility} records have invalid volatility values");
        if (invalidRsi > 0) issues.Add($"{invalidRsi} records have invalid RSI values");
        if (invalidVix > 0) issues.Add($"{invalidVix} records have invalid VIX values");

        // Check for data quality issues
        var duplicates = features.GroupBy(f => new { f.Symbol, f.Date }).Where(g => g.Count() > 1).Count();
        if (duplicates > 0) warnings.Add($"{duplicates} duplicate symbol-date combinations found");

        var recentDataCount = features.Count(f => f.Date >= DateTime.UtcNow.AddDays(-30));
        if (recentDataCount == 0) warnings.Add("No recent data (last 30 days) found");

        var isValid = !issues.Any();
        return new FeatureValidationResult(isValid, issues, warnings);
    }

    public async Task ExportSlippageCsvAsync(string path, DateTime from, DateTime to)
    {
        _logger.LogInformation("Exporting slippage data to CSV: {Path} from {From} to {To}", path, from, to);

        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            var fills = await context.FillsLog
                .Where(f => f.TimeUtc >= from && f.TimeUtc <= to)
                .OrderBy(f => f.TimeUtc)
                .ToListAsync();

            if (!fills.Any())
            {
                _logger.LogWarning("No fill data found for date range {From} to {To}", from, to);
                return;
            }

            _logger.LogInformation("Found {Count} fills for slippage export", fills.Count);

            // Ensure directory exists
            var directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // Export to CSV
            using var writer = new StreamWriter(path);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

            // Write header
            csv.WriteField("SpreadPct");
            csv.WriteField("RankProb");
            csv.WriteField("ATR_Pct");
            csv.WriteField("VolumePct10d");
            csv.WriteField("Regime");
            csv.WriteField("Side");
            csv.WriteField("Hour");
            csv.WriteField("Label"); // Slippage in basis points
            csv.NextRecord();

            // Write data rows
            foreach (var fill in fills)
            {
                // Calculate slippage label: (FillPrice - MidPrice) / MidPrice * 10000 (in basis points)
                var slippageBps = fill.MidPrice != 0
                    ? (float)((fill.FillPrice - fill.MidPrice) / fill.MidPrice * 10000)
                    : 0f;

                csv.WriteField(fill.SpreadPct);
                csv.WriteField(fill.RankProb);
                csv.WriteField(fill.ATR_Pct);
                csv.WriteField(fill.VolumePct10d);
                csv.WriteField(fill.Regime);
                csv.WriteField(fill.Side);
                csv.WriteField(fill.TimeUtc.Hour);
                csv.WriteField(slippageBps);
                csv.NextRecord();
            }

            _logger.LogInformation("Successfully exported {Count} slippage records to {Path}", fills.Count, path);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting slippage data to CSV: {Path}", path);
            throw;
        }
    }

    private static float CalculateVolatilityRank(float volatility, IEnumerable<SignalFeature> allFeatures)
    {
        var volatilities = allFeatures.Select(f => f.Volatility).OrderBy(v => v).ToList();
        var rank = volatilities.BinarySearch(volatility);
        if (rank < 0) rank = ~rank;
        return (float)rank / volatilities.Count;
    }

    public async Task ExportRegimeCsvAsync(string path, DateTime from, DateTime to)
    {
        _logger.LogInformation("Exporting regime classification data to {Path} from {From} to {To}",
            path, from, to);

        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            // Get daily aggregated data for regime classification
            var dailyData = await context.Features
                .Where(f => f.Date >= from && f.Date <= to)
                .GroupBy(f => f.Date.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    AvgBreadthScore = g.Average(f => f.BreadthScore),
                    AvgVixLevel = g.Average(f => f.VixLevel),
                    // We'll need to calculate SPX returns and VIX changes separately
                })
                .OrderBy(d => d.Date)
                .ToListAsync();

            if (!dailyData.Any())
            {
                _logger.LogWarning("No daily data found for regime export from {From} to {To}", from, to);
                return;
            }

            var regimeRows = new List<RegimeRow>();

            for (int i = 1; i < dailyData.Count; i++) // Start from 1 to calculate changes
            {
                var current = dailyData[i];
                var previous = dailyData[i - 1];

                // Calculate SPX return (simulated - in production this would come from actual SPX data)
                var spxRet = (float)((current.AvgVixLevel / previous.AvgVixLevel) - 1) * -0.5f; // Inverse correlation approximation

                // Calculate VIX change
                var vixChange = (float)((current.AvgVixLevel / previous.AvgVixLevel) - 1);

                // Apply historical labeling rules
                var regimeLabel = CalculateRegimeLabel(spxRet, current.AvgVixLevel, vixChange);

                regimeRows.Add(new RegimeRow(
                    current.Date,
                    spxRet,
                    (float)current.AvgVixLevel,
                    vixChange,
                    (float)current.AvgBreadthScore,
                    (uint)regimeLabel
                ));
            }

            // Write to CSV
            using var writer = new StringWriter();
            using var csv = new CsvHelper.CsvWriter(writer, System.Globalization.CultureInfo.InvariantCulture);

            // Write header
            csv.WriteField("Date");
            csv.WriteField("SPX_Ret");
            csv.WriteField("VIX_Level");
            csv.WriteField("VIX_Change");
            csv.WriteField("Breadth_Score");
            csv.WriteField("RegimeLabel");
            csv.NextRecord();

            // Write data
            foreach (var row in regimeRows)
            {
                csv.WriteField(row.Date.ToString("yyyy-MM-dd"));
                csv.WriteField(row.SPX_Ret);
                csv.WriteField(row.VIX_Level);
                csv.WriteField(row.VIX_Change);
                csv.WriteField(row.Breadth_Score);
                csv.WriteField(row.RegimeLabel);
                csv.NextRecord();
            }

            await File.WriteAllTextAsync(path, writer.ToString());

            _logger.LogInformation("Exported {Count} regime classification records to {Path}",
                regimeRows.Count, path);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting regime classification data to {Path}", path);
            throw;
        }
    }

    private static MarketRegime CalculateRegimeLabel(float spxRet, double vixLevel, float vixChange)
    {
        // Historical label rules:
        // • Panic: VIX_Level ≥ 30 OR SPX_Ret < −0.03
        // • TrendingDown: SPX_Ret < −0.012 AND VIX_Change > 0
        // • TrendingUp: SPX_Ret > +0.012 AND VIX_Change < 0
        // • Else: Sideways

        if (vixLevel >= 30 || spxRet < -0.03f)
            return MarketRegime.Panic;

        if (spxRet < -0.012f && vixChange > 0)
            return MarketRegime.TrendingDown;

        if (spxRet > 0.012f && vixChange < 0)
            return MarketRegime.TrendingUp;

        return MarketRegime.Sideways;
    }
}

/// <summary>
/// Statistics about exported features
/// </summary>
public record FeatureExportStats(
    int TotalCount,
    int WinCount,
    int LossCount,
    double WinRate,
    Dictionary<string, int> SymbolCounts,
    DateTime FromDate,
    DateTime ToDate
);

/// <summary>
/// Result of feature validation
/// </summary>
public record FeatureValidationResult(
    bool IsValid,
    List<string> Issues,
    List<string> Warnings
);

/// <summary>
/// Position sizing features for ML training
/// </summary>
public class PositionSizingRow
{
    public float RankProb { get; set; }
    public float ATR_Pct { get; set; }
    public float AvgSpreadPct { get; set; }
    public float EquityPctRisk { get; set; }

    // Parameterless constructor for ML.NET
    public PositionSizingRow() { }

    // Constructor with parameters for convenience
    public PositionSizingRow(float rankProb, float atrPct, float avgSpreadPct, float equityPctRisk)
    {
        RankProb = rankProb;
        ATR_Pct = atrPct;
        AvgSpreadPct = avgSpreadPct;
        EquityPctRisk = equityPctRisk;
    }
}
