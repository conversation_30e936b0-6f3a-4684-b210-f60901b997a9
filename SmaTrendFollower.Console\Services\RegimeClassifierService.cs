using Microsoft.ML;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Text.Json;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for ML-based market regime classification service
/// </summary>
public interface IRegimeClassifierService
{
    /// <summary>
    /// Detects current market regime using ML model
    /// </summary>
    Task<MarketRegime> DetectTodayAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets cached regime classification if available
    /// </summary>
    Task<MarketRegime?> GetCachedRegimeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Forces model reload from disk
    /// </summary>
    Task ReloadModelAsync();

    /// <summary>
    /// Gets model version information
    /// </summary>
    Task<long> GetModelVersionAsync();
}

/// <summary>
/// Production-ready ML-based market regime classification service.
/// Uses FastForest model to classify market conditions into 4 regimes:
/// Sideways, TrendingUp, TrendingDown, Panic.
/// Supports hot-reload of models via Redis versioning.
/// </summary>
public sealed class RegimeClassifierService : IRegimeClassifierService, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IBreadthService _breadthService;
    private readonly ILogger<RegimeClassifierService> _logger;
    private readonly MLContext _mlContext;

    private PredictionEngine<RegimeInput, RegimeOutput>? _predictionEngine;
    private long _modelVersion = 0;
    private readonly SemaphoreSlim _reloadLock = new(1, 1);
    private bool _disposed;

    private const string ModelPath = "Model/regime_model.zip";
    private const string ModelVersionKey = "model:regime:version";
    private const string RegimeCacheKey = "regime:today";

    public RegimeClassifierService(
        IMarketDataService marketDataService,
        IOptimizedRedisConnectionService redisService,
        IBreadthService breadthService,
        ILogger<RegimeClassifierService> logger)
    {
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _breadthService = breadthService ?? throw new ArgumentNullException(nameof(breadthService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _mlContext = new MLContext(seed: 42);

        // Load model on startup
        _ = Task.Run(async () => await ReloadModelAsync());
    }

    public async Task<MarketRegime> DetectTodayAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Check for model updates
            await CheckAndReloadModelAsync();

            if (_predictionEngine == null)
            {
                _logger.LogWarning("Regime classification model not loaded, returning Sideways");
                return MarketRegime.Sideways;
            }

            // Get required data for classification
            var (spxRet, vixLevel, vixChange) = await GetMarketDataAsync(cancellationToken);
            var breadthScore = await GetBreadthScoreAsync(cancellationToken);

            var input = new RegimeInput(spxRet, vixLevel, vixChange, breadthScore);
            var prediction = _predictionEngine.Predict(input);
            
            var regime = (MarketRegime)prediction.PredictedLabel;
            var confidence = prediction.Score.Max();

            // Cache the result
            await CacheRegimeAsync(regime, confidence);

            // Update metrics
            MetricsRegistry.RegimeState.Set((int)regime);
            
            _logger.LogInformation("Regime classified as {Regime} with confidence {Confidence:P1} " +
                                 "(SPX: {SpxRet:P2}, VIX: {VixLevel:F1}, VIXΔ: {VixChange:P2}, Breadth: {BreadthScore:F2})",
                regime, confidence, spxRet, vixLevel, vixChange, breadthScore);

            return regime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting market regime");
            return MarketRegime.Sideways; // Safe default
        }
    }

    public async Task<MarketRegime?> GetCachedRegimeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var cachedValue = await database.StringGetAsync(RegimeCacheKey);
            
            if (cachedValue.HasValue && Enum.TryParse<MarketRegime>(cachedValue!, out var regime))
            {
                return regime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving cached regime");
        }

        return null;
    }

    public async Task ReloadModelAsync()
    {
        await _reloadLock.WaitAsync();
        try
        {
            if (!File.Exists(ModelPath))
            {
                _logger.LogWarning("Regime model file not found at {ModelPath}", ModelPath);
                return;
            }

            var model = _mlContext.Model.Load(ModelPath, out _);
            _predictionEngine?.Dispose();
            _predictionEngine = _mlContext.Model.CreatePredictionEngine<RegimeInput, RegimeOutput>(model);

            // Update version
            var database = await _redisService.GetDatabaseAsync();
            var newVersion = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            await database.StringSetAsync(ModelVersionKey, newVersion, RedisKeyConstants.RedisKeyTTL.MLModel);
            _modelVersion = newVersion;

            _logger.LogInformation("Regime classification model reloaded successfully (version {Version})", _modelVersion);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading regime classification model");
        }
        finally
        {
            _reloadLock.Release();
        }
    }

    public async Task<long> GetModelVersionAsync()
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var version = await database.StringGetAsync(ModelVersionKey);
            return version.HasValue ? (long)version : 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting model version");
            return 0;
        }
    }

    private async Task CheckAndReloadModelAsync()
    {
        try
        {
            var currentVersion = await GetModelVersionAsync();
            if (currentVersion != _modelVersion)
            {
                await ReloadModelAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking model version");
        }
    }

    private async Task<(float spxRet, float vixLevel, float vixChange)> GetMarketDataAsync(CancellationToken cancellationToken)
    {
        try
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-3); // Get a few days to ensure we have data

            // Fetch SPX data
            var spxBars = await _marketDataService.GetIndexBarsAsync("I:SPX", startDate, endDate);
            var spxBarsList = spxBars.ToList();

            // Fetch VIX data
            var vixBars = await _marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);
            var vixBarsList = vixBars.ToList();

            if (spxBarsList.Count < 2 || vixBarsList.Count < 2)
            {
                _logger.LogWarning("Insufficient market data for regime classification");
                return (0f, 20f, 0f); // Safe defaults
            }

            // Calculate returns and changes
            var spxRet = (float)((spxBarsList[^1].Close / spxBarsList[^2].Close) - 1);
            var vixLevel = (float)vixBarsList[^1].Close;
            var vixChange = (float)((vixBarsList[^1].Close / vixBarsList[^2].Close) - 1);

            return (spxRet, vixLevel, vixChange);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching market data for regime classification");
            return (0f, 20f, 0f); // Safe defaults
        }
    }

    private async Task<float> GetBreadthScoreAsync(CancellationToken cancellationToken)
    {
        try
        {
            var breadthAnalysis = await _breadthService.GetCachedBreadthAsync();
            if (breadthAnalysis.HasValue)
            {
                return (float)(breadthAnalysis.Value.BreadthScore / 100m); // Normalize to 0-1
            }

            // If no cached data, try to calculate fresh
            var freshAnalysis = await _breadthService.CalculateMarketBreadthAsync(cancellationToken);
            return (float)(freshAnalysis.BreadthScore / 100m);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting breadth score for regime classification");
            return 0.5f; // Neutral default
        }
    }

    private async Task CacheRegimeAsync(MarketRegime regime, float confidence)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var regimeData = new
            {
                Regime = regime.ToString(),
                Confidence = confidence,
                DetectedAt = DateTime.UtcNow,
                Version = _modelVersion
            };

            await database.StringSetAsync(RegimeCacheKey, JsonSerializer.Serialize(regimeData), 
                RedisKeyConstants.RedisKeyTTL.Regime);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching regime classification");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _predictionEngine?.Dispose();
            _reloadLock?.Dispose();
            _disposed = true;
        }
    }
}
