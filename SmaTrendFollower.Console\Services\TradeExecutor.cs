using SmaTrendFollower.Models;
using SmaTrendFollower.Extensions;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Data;
using SmaTrendFollower.MachineLearning.Prediction;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Services;

public sealed class TradeExecutor : ITradeExecutor
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly IStopManager _stopManager;
    private readonly ISlippageEstimator _slippageEstimator;
    private readonly ISlippageForecasterService _slippageForecaster;
    private readonly IDbContextFactory<MLFeaturesDbContext> _dbContextFactory;
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<TradeExecutor> _logger;
    private readonly AnomalyDetectorService? _anomalyDetector;

    public TradeExecutor(
        IAlpacaClientFactory clientFactory,
        IStopManager stopManager,
        ISlippageEstimator slippageEstimator,
        ISlippageForecasterService slippageForecaster,
        IDbContextFactory<MLFeaturesDbContext> dbContextFactory,
        IMarketDataService marketDataService,
        ILogger<TradeExecutor> logger,
        AnomalyDetectorService? anomalyDetector = null)
    {
        _clientFactory = clientFactory;
        _stopManager = stopManager;
        _slippageEstimator = slippageEstimator;
        _slippageForecaster = slippageForecaster;
        _dbContextFactory = dbContextFactory;
        _marketDataService = marketDataService;
        _logger = logger;
        _anomalyDetector = anomalyDetector; // Optional dependency
    }

    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        if (quantity <= 0)
        {
            _logger.LogWarning("Invalid quantity {Quantity} for {Symbol}, skipping trade", quantity, signal.Symbol);
            return;
        }

        // Check if trading is halted for this symbol due to anomaly detection
        if (_anomalyDetector != null && await _anomalyDetector.IsTradingHaltedAsync(signal.Symbol))
        {
            _logger.LogWarning("Trade execution blocked for {Symbol} - trading halted due to anomaly detection", signal.Symbol);
            MetricsRegistry.TradesBlocked.WithLabels("anomaly_halt", signal.Symbol).Inc();
            return;
        }

        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object?>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Cancel existing orders for this symbol
                await CancelExistingOrdersAsync(tradingClient, signal.Symbol);

                // Get current quote for slippage prediction
                var quote = await GetCurrentQuoteAsync(signal.Symbol);

                // Calculate dynamic entry price using slippage forecaster
                var entryPrice = CalculateDynamicLimitPrice(signal, quote);

                // Calculate stop-loss price: entry - 2×ATR
                var stopLossPrice = entryPrice - (2 * signal.Atr);

                // Submit Limit-on-Open buy order
                // For now, use whole shares only (round up to ensure we have at least 1 share)
                var orderQuantity = Math.Max(1, (int)Math.Ceiling(quantity));
                var buyOrder = new NewOrderRequest(signal.Symbol, orderQuantity, Alpaca.Markets.OrderSide.Buy, SmaOrderType.Limit.ToAlpacaOrderType(), TimeInForce.Day)
                {
                    LimitPrice = entryPrice
                };

                _logger.LogInformation("Submitting buy order for {Symbol}: Quantity={Quantity}, OrderType=Limit, LimitPrice={LimitPrice:C}, StopLoss={StopLoss:C}",
                    signal.Symbol, orderQuantity, entryPrice, stopLossPrice);

                // Add timeout to prevent hanging
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var submittedOrder = await tradingClient.PostOrderAsync(buyOrder, cts.Token);

                _logger.LogInformation("Successfully submitted buy order for {Symbol}: OrderId={OrderId}, Quantity={Quantity}, LimitPrice={LimitPrice:C}",
                    signal.Symbol, submittedOrder.OrderId, quantity, entryPrice);

                // Record order submission for slippage tracking
                if (quote != null)
                {
                    await RecordOrderSubmissionAsync(submittedOrder, signal, quote, entryPrice, orderQuantity);
                }

                // Monitor order for fills and log to database
                _ = Task.Run(async () => await MonitorOrderFillAsync(submittedOrder.OrderId, signal, quote, entryPrice, orderQuantity));

                // Record successful trade execution
                MetricsRegistry.TradesTotal.WithLabels("Buy", signal.Symbol).Inc();

                // Set initial trailing stop using StopManager
                // Note: In production, you might want to wait for the buy order to fill before setting the stop
                await _stopManager.SetInitialStopAsync(signal.Symbol, entryPrice, signal.Atr, quantity, cts.Token);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                MetricsRegistry.OrderFailures.WithLabels(ex.GetType().Name, signal.Symbol).Inc();
                return null;
            }
        }, $"ExecuteTrade-{signal.Symbol}");
    }

    private async Task CancelExistingOrdersAsync(IAlpacaTradingClient tradingClient, string symbol)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object?>(async () =>
        {
            try
            {
                var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
                {
                    OrderStatusFilter = OrderStatusFilter.Open,
                    LimitOrderNumber = 100
                });

                var symbolOrders = openOrders.Where(o => o.Symbol == symbol).ToList();

                foreach (var order in symbolOrders)
                {
                    await tradingClient.CancelOrderAsync(order.OrderId, CancellationToken.None);
                    _logger.LogInformation("Cancelled existing order {OrderId} for {Symbol}", order.OrderId, symbol);
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error cancelling existing orders for {Symbol}", symbol);
                return null;
            }
        }, $"CancelOrders-{symbol}");
    }

    private decimal CalculateDynamicLimitPrice(TradingSignal signal, IQuote? quote)
    {
        try
        {
            if (quote == null || !_slippageForecaster.IsReady)
            {
                // Fallback to static pricing
                _logger.LogDebug("Using static limit pricing for {Symbol} (quote: {HasQuote}, forecaster ready: {IsReady})",
                    signal.Symbol, quote != null, _slippageForecaster.IsReady);
                return signal.Price * 1.002m;
            }

            // Create signal features for slippage prediction
            var features = new SlippageSignalFeatures(
                Symbol: signal.Symbol,
                RankProb: 0.65f, // Default - will be enhanced with actual ML probability
                ATR_Pct: (float)(signal.Atr / signal.Price * 100),
                VolumePct10d: 100f, // Default - will be enhanced with actual volume data
                Regime: 0.5f, // Default - will be enhanced with actual market regime
                Side: SmaOrderSide.Buy
            );

            // Create quote context
            var quoteContext = new QuoteContext(
                MidPrice: (quote.BidPrice + quote.AskPrice) / 2,
                SpreadPct: (float)((quote.AskPrice - quote.BidPrice) / ((quote.BidPrice + quote.AskPrice) / 2) * 100),
                TimestampUtc: DateTime.UtcNow
            );

            // Get slippage prediction in basis points
            var predictedSlippageBps = _slippageForecaster.PredictBps(features, quoteContext);

            // Convert to price offset (positive slippage means we need to pay more)
            var slippageOffset = signal.Price * (decimal)predictedSlippageBps / 10000m;

            // Calculate dynamic limit price: base price + predicted slippage + small buffer
            var dynamicPrice = signal.Price + slippageOffset + (signal.Price * 0.001m); // 0.1% buffer

            _logger.LogDebug("Dynamic pricing for {Symbol}: Base={BasePrice:C}, Predicted Slippage={SlippageBps:F2}bps, Final={FinalPrice:C}",
                signal.Symbol, signal.Price, predictedSlippageBps, dynamicPrice);

            return dynamicPrice;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating dynamic limit price for {Symbol} - using fallback", signal.Symbol);
            return signal.Price * 1.002m; // Fallback to static pricing
        }
    }

    private async Task<IQuote?> GetCurrentQuoteAsync(string symbol)
    {
        try
        {
            using var dataClient = _clientFactory.CreateDataClient();
            var quote = await dataClient.GetLatestQuoteAsync(new LatestMarketDataRequest(symbol));
            return quote;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get current quote for {Symbol}", symbol);
            return null;
        }
    }

    private async Task RecordOrderSubmissionAsync(IOrder order, TradingSignal signal, IQuote quote, decimal entryPrice, int orderQuantity)
    {
        try
        {
            var submission = new OrderSubmissionRecord(
                OrderId: order.OrderId.ToString(),
                Symbol: signal.Symbol,
                Side: SmaOrderSide.Buy,
                Quantity: orderQuantity,
                LimitPrice: entryPrice,
                MarketPrice: quote.AskPrice, // Use ask price as market price for buy orders
                BidPrice: quote.BidPrice,
                AskPrice: quote.AskPrice,
                Spread: quote.AskPrice - quote.BidPrice,
                SubmissionTime: DateTime.UtcNow,
                OrderType: "LIMIT"
            );

            await _slippageEstimator.RecordOrderSubmissionAsync(submission);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to record order submission for {OrderId}", order.OrderId);
        }
    }

    private async Task MonitorOrderFillAsync(Guid orderId, TradingSignal signal, IQuote? quote, decimal entryPrice, int orderQuantity)
    {
        try
        {
            var endTime = DateTime.UtcNow.AddMinutes(30); // Monitor for up to 30 minutes
            var rateLimitHelper = _clientFactory.GetRateLimitHelper();

            while (DateTime.UtcNow < endTime)
            {
                await Task.Delay(TimeSpan.FromSeconds(10)); // Check every 10 seconds

                var order = await rateLimitHelper.ExecuteAsync(async () =>
                {
                    using var tradingClient = _clientFactory.CreateTradingClient();
                    return await tradingClient.GetOrderAsync(orderId);
                }, $"MonitorOrder-{orderId}");

                if (order == null) continue;

                if (order.OrderStatus == OrderStatus.Filled)
                {
                    _logger.LogInformation("Order filled: OrderId={OrderId}, FilledPrice=${Price:F2}",
                        orderId, order.AverageFillPrice ?? 0);

                    // Record fill for slippage tracking
                    await RecordOrderFillAsync(order, signal);

                    // Log fill to database for ML training
                    await LogFillToDatabaseAsync(order, signal, quote, entryPrice);
                    break;
                }

                if (order.OrderStatus == OrderStatus.Canceled ||
                    order.OrderStatus == OrderStatus.Rejected ||
                    order.OrderStatus == OrderStatus.Expired)
                {
                    _logger.LogWarning("Order not filled: OrderId={OrderId}, Status={Status}",
                        orderId, order.OrderStatus);
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring order fill for {OrderId}", orderId);
        }
    }

    private async Task RecordOrderFillAsync(IOrder order, TradingSignal signal)
    {
        try
        {
            var fill = new OrderFillRecord(
                OrderId: order.OrderId.ToString(),
                Symbol: signal.Symbol,
                Side: SmaOrderSide.Buy,
                FilledQuantity: order.FilledQuantity,
                FillPrice: order.AverageFillPrice ?? 0m,
                FillTime: order.UpdatedAtUtc ?? DateTime.UtcNow,
                Exchange: "ALPACA",
                Commission: 0m // Alpaca doesn't charge commissions for stocks
            );

            await _slippageEstimator.RecordOrderFillAsync(fill);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to record order fill for {OrderId}", order.OrderId);
        }
    }

    private async Task LogFillToDatabaseAsync(IOrder order, TradingSignal signal, IQuote? quote, decimal entryPrice)
    {
        try
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            // Calculate slippage features
            var fillPrice = order.AverageFillPrice ?? 0;
            var midPrice = quote != null ? (quote.BidPrice + quote.AskPrice) / 2 : fillPrice;
            var spreadPct = quote != null ? (float)((quote.AskPrice - quote.BidPrice) / midPrice * 100) : 0f;

            // Calculate actual slippage in basis points
            var actualSlippageBps = midPrice != 0 ? (float)((fillPrice - midPrice) / midPrice * 10000) : 0f;

            // Record slippage metrics
            MetricsRegistry.SlippageActualBps.Observe(actualSlippageBps);

            // If we have the predicted slippage, calculate and record the prediction error
            if (_slippageForecaster.IsReady && quote != null)
            {
                try
                {
                    var features = new SlippageSignalFeatures(
                        Symbol: signal.Symbol,
                        RankProb: 0.65f, // Default - will be enhanced with actual ML probability
                        ATR_Pct: (float)(signal.Atr / signal.Price * 100),
                        VolumePct10d: 100f, // Default - will be enhanced with actual volume data
                        Regime: 0.5f, // Default - will be enhanced with actual market regime
                        Side: SmaOrderSide.Buy
                    );

                    var quoteContext = new QuoteContext(
                        MidPrice: midPrice,
                        SpreadPct: spreadPct,
                        TimestampUtc: order.UpdatedAtUtc ?? DateTime.UtcNow
                    );

                    var predictedSlippageBps = _slippageForecaster.PredictBps(features, quoteContext);
                    var predictionErrorBps = Math.Abs(actualSlippageBps - predictedSlippageBps);
                    var slippageSavedBps = predictedSlippageBps - actualSlippageBps; // Positive = saved money

                    MetricsRegistry.SlippagePredictedBps.Observe(predictedSlippageBps);
                    MetricsRegistry.SlippagePredictionErrorBps.Observe(predictionErrorBps);

                    if (slippageSavedBps > 0)
                    {
                        MetricsRegistry.SlippageSavedTotalBps.Inc(slippageSavedBps);
                    }

                    _logger.LogDebug("Slippage metrics for {Symbol}: Actual={ActualBps:F2}bps, Predicted={PredictedBps:F2}bps, Error={ErrorBps:F2}bps, Saved={SavedBps:F2}bps",
                        signal.Symbol, actualSlippageBps, predictedSlippageBps, predictionErrorBps, slippageSavedBps);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error calculating slippage prediction metrics for {Symbol}", signal.Symbol);
                }
            }

            // Get additional market context
            var atrPct = (float)(signal.Atr / signal.Price * 100);

            // Create fill log entry
            var fillLog = new FillLog
            {
                TimeUtc = order.UpdatedAtUtc ?? DateTime.UtcNow,
                Symbol = signal.Symbol,
                Side = 0, // Buy = 0
                Qty = order.FilledQuantity,
                FillPrice = fillPrice,
                MidPrice = midPrice,
                SpreadPct = spreadPct,
                RankProb = 0.65f, // Default - will be enhanced with actual ML probability
                Regime = 0.5f, // Default - will be enhanced with actual market regime
                ATR_Pct = atrPct,
                VolumePct10d = 100f, // Default - will be enhanced with actual volume data
                CreatedAt = DateTime.UtcNow
            };

            context.FillsLog.Add(fillLog);
            await context.SaveChangesAsync();

            _logger.LogDebug("Logged fill to database: {Symbol} @ {FillPrice:C}", signal.Symbol, fillPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log fill to database for {Symbol}", signal.Symbol);
        }
    }
}
