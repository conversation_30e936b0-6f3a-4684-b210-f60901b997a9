using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Moq;
using SmaTrendFollower.Data;
using SmaTrendFollower.MachineLearning.Prediction;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Scheduling;
using Alpaca.Markets;
using Xunit;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for slippage forecaster system including TradeExecutor integration and job scheduling
/// </summary>
[Collection("Database")]
public class SlippageForecasterIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly Mock<IAlpacaClientFactory> _mockClientFactory;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<IStopManager> _mockStopManager;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly string _testDatabasePath;

    public SlippageForecasterIntegrationTests()
    {
        _testDatabasePath = Path.Combine(Path.GetTempPath(), $"test_slippage_integration_{Guid.NewGuid()}.db");
        
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add test database
        services.AddDbContextFactory<MLFeaturesDbContext>(options =>
            options.UseSqlite($"Data Source={_testDatabasePath}"));
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SlippageTraining:ModelOutputPath"] = Path.Combine(Path.GetTempPath(), "integration_test_slippage_model.zip"),
                ["SlippageTraining:NumberOfTrees"] = "10", // Small for fast testing
                ["SlippageTraining:NumberOfLeaves"] = "8",
                ["SlippageTraining:TrainingDataDays"] = "7"
            })
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Mock Alpaca services
        _mockClientFactory = new Mock<IAlpacaClientFactory>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockStopManager = new Mock<IStopManager>();
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        
        services.AddSingleton(_mockClientFactory.Object);
        services.AddSingleton(_mockStopManager.Object);
        services.AddSingleton(_mockMarketDataService.Object);
        services.AddSingleton(_mockRedisService.Object);
        
        // Add slippage services
        services.AddSingleton<ISlippageForecasterService, SlippageForecasterService>();
        services.AddScoped<ISlippageEstimator, SlippageEstimator>();
        
        // Add TradeExecutor
        services.AddScoped<TradeExecutor>();
        
        // Add job services
        services.AddScoped<SlippageForecasterRetrainerJob>();
        
        _serviceProvider = services.BuildServiceProvider();
        
        SetupMocks();
        InitializeDatabaseAsync().Wait();
    }

    private void SetupMocks()
    {
        // Setup Alpaca client factory
        _mockClientFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockClientFactory.Setup(x => x.CreateDataClient()).Returns(_mockDataClient.Object);
        _mockClientFactory.Setup(x => x.GetRateLimitHelper()).Returns(new Mock<IAlpacaRateLimitHelper>().Object);
        
        // Setup mock quote data
        var mockQuote = new Mock<IQuote>();
        mockQuote.Setup(x => x.BidPrice).Returns(149.95m);
        mockQuote.Setup(x => x.AskPrice).Returns(150.05m);
        
        var quoteDict = new Dictionary<string, IQuote> { ["AAPL"] = mockQuote.Object };
        _mockDataClient.Setup(x => x.GetLatestQuoteAsync(It.IsAny<LatestMarketDataRequest>()))
            .ReturnsAsync(quoteDict);
        
        // Setup mock order
        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.OrderId).Returns(Guid.NewGuid());
        mockOrder.Setup(x => x.Symbol).Returns("AAPL");
        mockOrder.Setup(x => x.OrderStatus).Returns(OrderStatus.Filled);
        mockOrder.Setup(x => x.FilledQuantity).Returns(100m);
        mockOrder.Setup(x => x.AverageFillPrice).Returns(150.02m);
        mockOrder.Setup(x => x.UpdatedAtUtc).Returns(DateTime.UtcNow);
        
        _mockTradingClient.Setup(x => x.PostOrderAsync(It.IsAny<NewOrderRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOrder.Object);
        
        _mockTradingClient.Setup(x => x.GetOrderAsync(It.IsAny<Guid>()))
            .ReturnsAsync(mockOrder.Object);
        
        _mockTradingClient.Setup(x => x.ListOrdersAsync(It.IsAny<ListOrdersRequest>()))
            .ReturnsAsync(new List<IOrder>());
    }

    private async Task InitializeDatabaseAsync()
    {
        using var context = await _serviceProvider.GetRequiredService<IDbContextFactory<MLFeaturesDbContext>>()
            .CreateDbContextAsync();
        await context.Database.EnsureCreatedAsync();
        
        // Seed some test fill data for training
        await SeedTestFillDataAsync(context);
    }

    private async Task SeedTestFillDataAsync(MLFeaturesDbContext context)
    {
        var random = new Random(42);
        var baseTime = DateTime.UtcNow.AddDays(-10);
        
        for (int i = 0; i < 50; i++)
        {
            var fillLog = new FillLog
            {
                TimeUtc = baseTime.AddHours(i * 2),
                Symbol = "AAPL",
                Side = i % 2,
                Qty = 100m,
                FillPrice = 150m + (decimal)(random.NextDouble() * 2 - 1), // ±$1 around $150
                MidPrice = 150m,
                SpreadPct = 0.1f,
                RankProb = 0.7f,
                Regime = 0.5f,
                ATR_Pct = 2.5f,
                VolumePct10d = 100f,
                CreatedAt = baseTime.AddHours(i * 2)
            };
            
            context.FillsLog.Add(fillLog);
        }
        
        await context.SaveChangesAsync();
    }

    [Fact]
    public async Task TradeExecutor_WithSlippageForecaster_ShouldUseDynamicPricing()
    {
        // Arrange
        var tradeExecutor = _serviceProvider.GetRequiredService<TradeExecutor>();
        var signal = new TradingSignal
        {
            Symbol = "AAPL",
            Price = 150m,
            Atr = 3.75m, // 2.5% ATR
            Timestamp = DateTime.UtcNow,
            Confidence = 0.8f
        };
        
        // Act
        await tradeExecutor.ExecuteTradeAsync(signal, 100m);
        
        // Assert
        // Verify that order was submitted
        _mockTradingClient.Verify(x => x.PostOrderAsync(
            It.Is<NewOrderRequest>(order => 
                order.Symbol == "AAPL" && 
                order.Quantity == 100 &&
                order.OrderType == OrderType.Limit),
            It.IsAny<CancellationToken>()), Times.Once);
        
        // Verify that quote was fetched for dynamic pricing
        _mockDataClient.Verify(x => x.GetLatestQuoteAsync(
            It.Is<LatestMarketDataRequest>(req => req.Symbols.Contains("AAPL"))), Times.AtLeastOnce);
    }

    [Fact]
    public async Task TradeExecutor_ShouldLogFillToDatabase()
    {
        // Arrange
        var tradeExecutor = _serviceProvider.GetRequiredService<TradeExecutor>();
        var signal = new TradingSignal
        {
            Symbol = "AAPL",
            Price = 150m,
            Atr = 3.75m,
            Timestamp = DateTime.UtcNow,
            Confidence = 0.8f
        };
        
        var initialFillCount = await GetFillCountAsync();
        
        // Act
        await tradeExecutor.ExecuteTradeAsync(signal, 100m);
        
        // Wait a bit for async fill monitoring to complete
        await Task.Delay(100);
        
        // Assert
        var finalFillCount = await GetFillCountAsync();
        finalFillCount.Should().BeGreaterThan(initialFillCount);
    }

    [Fact]
    public async Task SlippageForecasterRetrainerJob_ShouldExecuteSuccessfully()
    {
        // Arrange
        var job = _serviceProvider.GetRequiredService<SlippageForecasterRetrainerJob>();
        var mockJobContext = new Mock<Quartz.IJobExecutionContext>();
        
        // Act
        var executeAction = async () => await job.Execute(mockJobContext.Object);
        
        // Assert
        await executeAction.Should().NotThrowAsync();
    }

    [Fact]
    public async Task SlippageForecaster_ShouldHandleNoTrainingData()
    {
        // Arrange
        // Clear all fill data
        using var context = await _serviceProvider.GetRequiredService<IDbContextFactory<MLFeaturesDbContext>>()
            .CreateDbContextAsync();
        context.FillsLog.RemoveRange(context.FillsLog);
        await context.SaveChangesAsync();
        
        var forecaster = _serviceProvider.GetRequiredService<ISlippageForecasterService>();
        
        // Act
        var features = new SlippageSignalFeatures(
            Symbol: "AAPL",
            RankProb: 0.7f,
            ATR_Pct: 2.5f,
            VolumePct10d: 100f,
            Regime: 0.5f,
            Side: SmaOrderSide.Buy
        );
        
        var quote = new QuoteContext(
            MidPrice: 150m,
            SpreadPct: 0.1f,
            TimestampUtc: DateTime.UtcNow
        );
        
        var prediction = forecaster.PredictBps(features, quote);
        
        // Assert
        prediction.Should().BeInRange(-50f, 50f); // Should return reasonable fallback
        forecaster.IsReady.Should().BeFalse(); // Should not be ready without model
    }

    private async Task<int> GetFillCountAsync()
    {
        using var context = await _serviceProvider.GetRequiredService<IDbContextFactory<MLFeaturesDbContext>>()
            .CreateDbContextAsync();
        return await context.FillsLog.CountAsync();
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
        
        if (File.Exists(_testDatabasePath))
        {
            File.Delete(_testDatabasePath);
        }
        
        // Cleanup test model files
        var configuredPath = _serviceProvider?.GetRequiredService<IConfiguration>()["SlippageTraining:ModelOutputPath"];
        if (!string.IsNullOrEmpty(configuredPath) && File.Exists(configuredPath))
        {
            File.Delete(configuredPath);
        }
    }
}
