using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Scheduling;
using SmaTrendFollower.MachineLearning.Prediction;
using SmaTrendFollower.Services;
using SmaTrendFollower.Monitoring;
using StackExchange.Redis;
using System.IO;

namespace SmaTrendFollower.Tests.MachineLearning;

/// <summary>
/// Integration tests for ML model retraining and hot-reload functionality
/// </summary>
[Trait("Category", "Integration")]
public class MLRetrainerIntegrationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly IHost _host;
    private readonly IServiceScope _scope;

    public MLRetrainerIntegrationTests(ITestOutputHelper output)
    {
        _output = output;
        
        // Create test host with full services
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddFullTradingSystem(TestHelpers.GetTestConfiguration());
                services.AddSchedulingServices();
                services.AddLogging(builder => builder.AddXUnit(output));
            })
            .Build();

        _scope = _host.Services.CreateScope();
    }

    [Fact]
    public async Task MLModelRetrainerJob_ShouldExecuteSuccessfully()
    {
        // Arrange
        var retrainerJob = _scope.ServiceProvider.GetRequiredService<MLModelRetrainerJob>();
        var mockContext = new MockJobExecutionContext();

        // Act & Assert - Should not throw
        var executeTask = retrainerJob.Execute(mockContext);
        
        // Allow up to 5 minutes for training to complete
        await executeTask.WaitAsync(TimeSpan.FromMinutes(5));
        
        _output.WriteLine("✅ ML retrainer job executed successfully");
    }

    [Fact]
    public async Task SignalRanker_ShouldSupportHotReload()
    {
        // Arrange
        var signalRanker = _scope.ServiceProvider.GetRequiredService<ISignalRanker>();
        var redisService = _scope.ServiceProvider.GetService<OptimizedRedisConnectionService>();

        // Skip test if Redis is not available
        if (redisService == null)
        {
            _output.WriteLine("⚠️ Skipping test - Redis service not available");
            return;
        }

        var database = await redisService.GetDatabaseAsync();
        
        // Act - Simulate model version update
        var newVersion = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        await database.StringSetAsync("model:signal:version", newVersion, 
            RedisKeyConstants.RedisKeyTTL.MLModel);

        // Create test features
        var testFeatures = new Models.SignalFeatures(
            SmaGap: 0.05f,
            Volatility: 0.15f,
            Rsi: 45.0f,
            BreadthScore: 0.6f,
            VixLevel: 18.5f,
            SixMonthReturn: 0.12f,
            RelativeVolume: 1.2f,
            MarketRegime: 0.8f
        );

        // Act - Score should trigger hot-reload check
        var score = signalRanker.Score(testFeatures);

        // Assert
        score.Should().BeInRange(0.0f, 1.0f);
        _output.WriteLine($"✅ SignalRanker scored features: {score:F3}");
    }

    [Fact]
    public void MetricsRegistry_ShouldHaveMLMetrics()
    {
        // Assert - Verify ML metrics are properly defined
        MetricsRegistry.MLRetrainRuns.Should().NotBeNull();
        MetricsRegistry.MLModelAccuracy.Should().NotBeNull();
        MetricsRegistry.MLModelVersion.Should().NotBeNull();
        MetricsRegistry.MLRetrainDuration.Should().NotBeNull();
        MetricsRegistry.MLPredictions.Should().NotBeNull();
        MetricsRegistry.MLPredictionScores.Should().NotBeNull();

        _output.WriteLine("✅ All ML metrics are properly registered");
    }

    [Fact]
    public void RedisKeyConstants_ShouldHaveMLKeys()
    {
        // Assert - Verify ML Redis keys are properly defined
        RedisKeyConstants.RedisKeyTTL.MLModel.Should().Be(TimeSpan.FromDays(7));
        RedisKeyConstants.KeyPatterns.MLModel.Should().Be("model:*");
        RedisKeyConstants.KeyPatterns.MLMetadata.Should().Be("ml:*");
        
        // Verify pattern to TTL mapping
        RedisKeyConstants.PatternToTTL.Should().ContainKey(RedisKeyConstants.KeyPatterns.MLModel);
        RedisKeyConstants.PatternToTTL.Should().ContainKey(RedisKeyConstants.KeyPatterns.MLMetadata);

        _output.WriteLine("✅ All ML Redis key constants are properly defined");
    }

    [Fact]
    public async Task MLRetrainerJob_ShouldUpdateRedisVersion()
    {
        // Arrange
        var redisService = _scope.ServiceProvider.GetService<OptimizedRedisConnectionService>();
        
        // Skip test if Redis is not available
        if (redisService == null)
        {
            _output.WriteLine("⚠️ Skipping test - Redis service not available");
            return;
        }

        var database = await redisService.GetDatabaseAsync();
        var initialVersion = await database.StringGetAsync("model:signal:version");

        // Act - Simulate model file update
        var modelPath = Path.Combine("Model", "signal_model.zip");
        if (File.Exists(modelPath))
        {
            // Touch the file to update timestamp
            File.SetLastWriteTimeUtc(modelPath, DateTime.UtcNow);
        }

        // Update Redis version manually (simulating what retrainer does)
        var newVersion = File.Exists(modelPath) 
            ? File.GetLastWriteTimeUtc(modelPath).Ticks 
            : DateTime.UtcNow.Ticks;
            
        await database.StringSetAsync("model:signal:version", newVersion, 
            RedisKeyConstants.RedisKeyTTL.MLModel);

        // Assert
        var updatedVersion = await database.StringGetAsync("model:signal:version");
        updatedVersion.Should().NotBe(initialVersion);
        
        _output.WriteLine($"✅ Redis model version updated: {initialVersion} -> {updatedVersion}");
    }

    [Fact]
    public void SchedulingConfiguration_ShouldHaveCorrectCronSchedule()
    {
        // The retrainer should be scheduled for Sunday 6:00 PM ET (10:00 PM UTC)
        // Cron: "0 0 22 ? * SUN"
        
        // This test verifies the configuration is set correctly
        // We can't easily test the actual Quartz scheduling without a full integration test
        
        var expectedCron = "0 0 22 ? * SUN"; // Sunday 10:00 PM UTC (6:00 PM ET)
        
        // Assert - This is more of a documentation test
        expectedCron.Should().Be("0 0 22 ? * SUN");
        
        _output.WriteLine($"✅ ML retrainer scheduled for: {expectedCron} (Sunday 6:00 PM ET)");
    }

    public void Dispose()
    {
        _scope?.Dispose();
        _host?.Dispose();
    }
}

/// <summary>
/// Mock implementation of IJobExecutionContext for testing
/// </summary>
public class MockJobExecutionContext : Quartz.IJobExecutionContext
{
    public Quartz.IScheduler Scheduler => throw new NotImplementedException();
    public Quartz.ITrigger Trigger => throw new NotImplementedException();
    public Quartz.ICalendar Calendar => throw new NotImplementedException();
    public bool Recovering => false;
    public Quartz.TriggerKey RecoveringTriggerKey => throw new NotImplementedException();
    public int RefireCount => 0;
    public Quartz.JobDataMap MergedJobDataMap => new Quartz.JobDataMap();
    public Quartz.IJobDetail JobDetail => throw new NotImplementedException();
    public Quartz.IJob JobInstance => throw new NotImplementedException();
    public DateTimeOffset FireTimeUtc => DateTimeOffset.UtcNow;
    public DateTimeOffset? ScheduledFireTimeUtc => DateTimeOffset.UtcNow;
    public DateTimeOffset? PreviousFireTimeUtc => null;
    public DateTimeOffset? NextFireTimeUtc => null;
    public string FireInstanceId => Guid.NewGuid().ToString();
    public object Result { get; set; } = null!;
    public TimeSpan JobRunTime => TimeSpan.Zero;
    public CancellationToken CancellationToken => CancellationToken.None;

    public void Put(object key, object objectValue) { }
    public object Get(object key) => null!;
}
